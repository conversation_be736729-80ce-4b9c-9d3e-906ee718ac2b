import BrandsRepository from "@/modules/Brands/Brands";
import { type Brand, type BrandInfo } from "@/types";
import { defineStore } from "pinia";

interface State {
	brands: Brand[];
}

export const useBrandStore = defineStore("brand", {
	state: (): State => {
		return {
			brands: [],
		};
	},
	persist: {
		storage: sessionStorage,
	},
	getters: {
		getAccounts(state) {
			return (name: string | null): BrandInfo[] | [] => {
				if (state.brands && Array.isArray(state.brands) && state.brands.length > 0) {
					// If accountInfo exists, return accountInfo's id, name, and logo, otherwise return brandInfo's
					const accounts = state.brands
						.filter(brand => brand && (brand.brandInfo || brand.accountInfo))
						.map(({ brandInfo, accountInfo }): BrandInfo | null => {
							const info = accountInfo || brandInfo;
							if (!info || !info.id || !info.name) {
								return null;
							}
							return {
								id: info.id,
								name: info.name,
								logo: info.logo || '',
								time_zone: brandInfo?.time_zone || 'UTC',
							};
						})
						.filter((item): item is BrandInfo => item !== null);

					const uniqueAccounts = [
						...new Map(accounts.map((item) => [item.id, item])).values(),
					];

					const filteredAccounts = name
						? uniqueAccounts.filter((item) =>
								item?.name?.toLowerCase()?.includes(name?.toLowerCase()),
						  )
						: uniqueAccounts;

					filteredAccounts.sort((a, b) => (a.name || '').localeCompare(b.name || ''));

					return filteredAccounts;
				}
				return [];
			};
		},
		getAccountById: (state) => {
			return (accountId: number) => {
				const brand = state.brands.find(
					(brand) =>
						brand?.accountInfo?.id === accountId ||
						brand?.brandInfo?.id === accountId,
				);

				return brand?.accountInfo ?? brand?.brandInfo;
			};
		},
		getBrandsByAccountId: (state) => {
			return (accountId: number, name: string | null) => {
				if (!state.brands || !Array.isArray(state.brands)) {
					return [];
				}

				let brands = state.brands.filter(
					(brand) =>
						brand?.accountInfo?.id === accountId ||
						brand?.brandInfo?.id === accountId,
				);

				if (name) {
					brands = brands.filter((brand) =>
						brand?.brandInfo?.name?.toLowerCase()?.includes(name?.toLowerCase()),
					);
				}

				brands.sort((a, b) =>
					(a?.brandInfo?.name || '').localeCompare(b?.brandInfo?.name || '')
				);

				return brands;
			};
		},
		getBrandById: (state) => {
			return (brandId: number) => {
				if (!state.brands || !Array.isArray(state.brands)) {
					return undefined;
				}
				return state.brands.find((brand) => brand?.brandInfo?.id === brandId)
					?.brandInfo;
			};
		},
	},
	actions: {
		async get(): Promise<void> {
			try {
				const brands: Array<Brand> = await BrandsRepository.get();
				this.brands = brands ?? [];
				return Promise.resolve();
			} catch (error) {
				throw Promise.reject();
			}
		},

		reset(): void {
			this.brands = [];
		},
	},
});
