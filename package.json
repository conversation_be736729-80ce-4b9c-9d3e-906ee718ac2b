{"name": "dashboard", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "serve:dev": "vite --mode dev --port 8080 --host", "serve:beta": "vite --mode beta --port 8080", "serve:prod": "vite --mode production --port 8080", "build:dev": "vite build --mode dev", "build:beta": "vite build --mode beta", "build:prod": "vite build --mode production", "build": "run-p type-check build-only", "preview": "vite preview", "test:unit": "vitest --silent --run", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint:rome": "rome check .", "lint:eslint": "eslint --ext .vue --ignore-path .gitignore --fix src", "lint": "npm run lint:rome && npm run lint:eslint", "format": "prettier .  --write", "prepare": "husky install"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.556.0", "@heroicons/vue": "^2.0.18", "@hotelinking/ui": "^13.45.9", "@intlify/unplugin-vue-i18n": "^0.12.2", "@pinia/testing": "^0.1.3", "@tailwindcss/forms": "^0.5.3", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.3", "@vueuse/core": "^10.2.1", "@vueuse/integrations": "^11.3.0", "@vueuse/router": "^10.2.1", "apexcharts": "^4.7.0", "aws-amplify": "^5.3.7", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.9", "flush-promises": "^1.0.2", "lodash-es": "^4.17.21", "msw": "^1.2.2", "pinia": "^2.1.3", "pinia-plugin-persistedstate": "^3.1.0", "qrcode": "^1.5.4", "tailwindcss": "^3.3.2", "vue": "^3.3.4", "vue-i18n": "^9.2.2", "vue-router": "^4.2.2", "vue3-apexcharts": "^1.8.0", "vue3-draggable": "^2.0.9", "vue3-funnel-graph-js": "^0.0.3"}, "devDependencies": {"@faker-js/faker": "^8.0.2", "@playwright/test": "1.53.1", "@testing-library/vue": "^7.0.0", "@tsconfig/node18": "^2.0.1", "@types/jsdom": "^21.1.1", "@types/lodash-es": "^4.17.7", "@types/node": "^18.16.17", "@types/vue-i18n": "^7.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-vue": "^4.2.3", "@vue/test-utils": "^2.2.7", "@vue/tsconfig": "^0.4.0", "autoprefixer": "^10.4.14", "eslint": "^8.43.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-vue": "^9.15.1", "husky": "^8.0.3", "jsdom": "^22.1.0", "npm-run-all": "^1.7.0", "postcss": "^8.4.24", "prettier": "^2.8.8", "rome": "^12.1.3", "typescript": "~5.0.4", "vite": "^4.3.9", "vite-plugin-node-polyfills": "^0.10.0", "vitest": "^0.32.0", "vue-tsc": "^1.6.5"}, "msw": {"workerDirectory": "public"}}