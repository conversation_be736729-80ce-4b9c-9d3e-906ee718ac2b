import { test, expect } from '@playwright/test';

test('simple login page navigation', async ({ page }) => {
  // Clear any existing session data
  await page.goto('/');
  await page.evaluate(() => {
    sessionStorage.clear();
    localStorage.clear();
  });
  
  console.log('Navigating to login page...');
  await page.goto('/dashboard/login', { waitUntil: 'domcontentloaded', timeout: 30000 });
  console.log('Login page loaded successfully');
  
  // Just check if the page loads without errors
  await page.waitForTimeout(3000);
  
  // Check if we can find the login form
  const emailField = await page.locator('[name="email"]').count();
  console.log('Email field found:', emailField > 0);
  
  // Take a screenshot for debugging
  await page.screenshot({ path: 'login-page-test.png', fullPage: true });
  
  expect(emailField).toBeGreaterThan(0);
});
