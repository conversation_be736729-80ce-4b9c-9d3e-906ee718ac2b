import { test, expect } from '@playwright/test';

test('debug login page', async ({ page }) => {
  console.log('Starting login debug test...');
  
  // Add page error and close listeners for debugging
  page.on('pageerror', (error) => {
    console.error('Page error:', error);
  });
  
  page.on('close', () => {
    console.log('Page was closed!');
  });
  
  page.on('crash', () => {
    console.log('Page crashed!');
  });
  
  try {
    console.log('Navigating to login page...');
    await page.goto('/dashboard/login', { waitUntil: 'domcontentloaded', timeout: 30000 });
    console.log('Login page loaded, current URL:', page.url());
    
    // Take a screenshot to see what's on the page
    await page.screenshot({ path: 'debug-login-page.png', fullPage: true });
    console.log('Screenshot saved as debug-login-page.png');
    
    // Wait a bit for the page to fully load
    await page.waitForTimeout(3000);
    
    // Check if the page is still open
    console.log('Page is closed after wait:', page.isClosed());
    
    // Try to find the email field
    console.log('Looking for email field...');
    const emailField = await page.locator('[name="email"]').count();
    console.log('Email field count:', emailField);
    
    if (emailField > 0) {
      console.log('Email field found, trying to fill it...');
      await page.locator('[name="email"]').fill('<EMAIL>');
      console.log('Email filled successfully');
    } else {
      console.log('Email field not found');
      // Let's see what's actually on the page
      const pageContent = await page.content();
      console.log('Page content length:', pageContent.length);
      console.log('Page title:', await page.title());
    }
    
  } catch (error) {
    console.error('Debug login test failed:', error);
    console.log('Current URL:', page.url());
    console.log('Page is closed:', page.isClosed());
    
    // Try to take a screenshot even if there's an error
    try {
      await page.screenshot({ path: 'debug-login-error.png', fullPage: true });
      console.log('Error screenshot saved as debug-login-error.png');
    } catch (screenshotError) {
      console.log('Could not take error screenshot:', (screenshotError as Error).message);
    }
    
    throw error;
  }
});
