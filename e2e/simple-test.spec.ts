import { test, expect } from '@playwright/test';

test('simple navigation test', async ({ page }) => {
  console.log('Starting simple test...');
  
  try {
    console.log('Navigating to home page...');
    await page.goto('/', { waitUntil: 'domcontentloaded', timeout: 30000 });
    console.log('Navigation successful, current URL:', page.url());
    
    // Just check if the page loads
    await page.waitForTimeout(2000);
    console.log('Page loaded successfully');
    
  } catch (error) {
    console.error('Simple test failed:', error);
    console.log('Page is closed:', page.isClosed());
    throw error;
  }
});
