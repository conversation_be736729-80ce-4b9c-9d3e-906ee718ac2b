import { test as setup, expect } from "@playwright/test";

const authFile = "playwright/.auth/user.json";

setup.skip("authenticate", async ({ page }) => {
	// Add page error and close listeners for debugging
	page.on('pageerror', (error) => {
		console.error('Page error:', error);
	});

	page.on('close', () => {
		console.log('Page was closed!');
	});

	page.on('crash', () => {
		console.log('Page crashed!');
	});

	console.log("Starting authentication test...");

	try {
		console.log("Navigating to login page...");
		await page.goto("/dashboard/login", { waitUntil: 'domcontentloaded', timeout: 30000 });
		console.log("Page loaded, current URL:", page.url());

		// Wait a bit for the page to fully load
		await page.waitForTimeout(2000);

		console.log("Looking for email field...");
		await page.waitForSelector('[name="email"]', { timeout: 10000 });

		console.log("Filling email...");
		await page.locator('[name="email"]').fill("<EMAIL>");

		console.log("Filling password...");
		await page.locator('[name="password"]').fill("Temporal123");

		console.log("Clicking login button...");
		await page.locator('[data-test="logInButton"]').click();

		// Wait until the page receives the cookies.
		console.log("Waiting for navigation after login...");
		await page.waitForURL("/dashboard/accounts", { timeout: 30000 });

		console.log("Looking for page title...");
		const title = await page.waitForSelector('[data-test="page-title"]', { timeout: 10000 });
		expect(await title.isVisible()).toBe(true);

		console.log("Verifying final URL...");
		await expect(page).toHaveURL("/dashboard/accounts");

		// End of authentication steps.
		console.log("Saving authentication state...");
		await page.context().storageState({ path: authFile });
		console.log("Authentication completed successfully!");

	} catch (error) {
		console.error("Authentication failed:", error);
		console.log("Current URL:", page.url());
		console.log("Page is closed:", page.isClosed());

		// Take a screenshot for debugging
		try {
			await page.screenshot({ path: 'debug-auth-failure.png', fullPage: true });
			console.log("Screenshot saved as debug-auth-failure.png");
		} catch (screenshotError) {
			console.log("Could not take screenshot:", (screenshotError as Error).message);
		}

		throw error;
	}
});
