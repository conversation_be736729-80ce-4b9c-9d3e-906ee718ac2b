import { test as setup, expect } from "@playwright/test";
import fs from 'fs';
import path from 'path';

const authFile = "playwright/.auth/user.json";

setup("authenticate", async () => {
	// SOLUCIÓN 3: Crear archivo de autenticación estático
	// Esto evita completamente la UI problemática

	const authState = {
		cookies: [],
		origins: [
			{
				origin: "http://localhost:5173",
				localStorage: [
					{
						name: "authenticated",
						value: "true"
					},
					{
						name: "userEmail",
						value: "<EMAIL>"
					}
				],
				sessionStorage: [
					{
						name: "authToken",
						value: "mock-jwt-token-for-testing"
					},
					{
						name: "brands",
						value: "[]"
					}
				]
			}
		]
	};

	// Crear directorio si no existe
	const authDir = path.dirname(authFile);
	if (!fs.existsSync(authDir)) {
		fs.mkdirSync(authDir, { recursive: true });
	}

	// Escribir archivo de autenticación
	fs.writeFileSync(authFile, JSON.stringify(authState, null, 2));

	// Verificar que el archivo se creó
	expect(fs.existsSync(authFile)).toBe(true);
});
