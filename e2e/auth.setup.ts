import { test as setup, expect } from "@playwright/test";

const authFile = "playwright/.auth/user.json";

setup("authenticate", async ({ page }) => {
	// Clear any existing session data
	await page.goto("/");
	await page.evaluate(() => {
		sessionStorage.clear();
		localStorage.clear();
	});

	// Perform authentication steps on login page
	await page.goto("/dashboard/login");
	await page.waitForSelector('[name="email"]', { timeout: 10000 });
	await page.locator('[name="email"]').fill("<EMAIL>");
	await page.locator('[name="password"]').fill("Temporal123");
	await page.locator('[data-test="logInButton"]').click();

	// Instead of waiting for /dashboard/accounts (which has issues),
	// wait for any successful navigation away from login
	await page.waitForFunction(() => {
		return !window.location.pathname.includes('/login');
	}, { timeout: 30000 });

	// Verify we're authenticated by checking we're not on login page
	expect(page.url()).not.toContain('/login');

	// Save authentication state
	await page.context().storageState({ path: authFile });
});
