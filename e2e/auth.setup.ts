import { test as setup, expect } from "@playwright/test";

const authFile = "playwright/.auth/user.json";

setup.skip("authenticate", async ({ page }) => {
	// Clear any existing session data to start fresh
	await page.goto("/");
	await page.evaluate(() => {
		sessionStorage.clear();
		localStorage.clear();
	});

	// Perform authentication steps
	await page.goto("/dashboard/login");
	await page.waitForSelector('[name="email"]', { timeout: 10000 });
	await page.locator('[name="email"]').fill("<EMAIL>");
	await page.locator('[name="password"]').fill("Temporal123");
	await page.locator('[data-test="logInButton"]').click();

	// Wait until the page receives the cookies and navigates
	await page.waitForURL("/dashboard/accounts", { timeout: 30000 });

	// Verify we're on the correct page
	const title = await page.waitForSelector('[data-test="page-title"]', { timeout: 10000 });
	expect(await title.isVisible()).toBe(true);
	await expect(page).toHaveURL("/dashboard/accounts");

	// Save authentication state
	await page.context().storageState({ path: authFile });
});
