import { test as setup, expect } from "@playwright/test";

const authFile = "playwright/.auth/user.json";

setup.skip("authenticate", async ({ page, context }) => {
	// Add error handling for page errors
	page.on('pageerror', (error) => {
		console.log('Page error detected:', error.message);
	});

	page.on('close', () => {
		console.log('Page was closed unexpectedly');
	});

	try {
		// Clear any existing session data to start fresh
		await page.goto("/");
		await page.evaluate(() => {
			sessionStorage.clear();
			localStorage.clear();
		});

		// Navigate to login page with more robust error handling
		console.log('Navigating to login page...');
		await page.goto("/dashboard/login", {
			waitUntil: 'domcontentloaded',
			timeout: 30000
		});

		// Wait for the page to stabilize
		await page.waitForTimeout(2000);

		// Check if page is still open
		if (page.isClosed()) {
			throw new Error('Page closed during navigation to login');
		}

		console.log('Waiting for email field...');
		await page.waitForSelector('[name="email"]', { timeout: 15000 });

		console.log('Filling credentials...');
		await page.locator('[name="email"]').fill("<EMAIL>");
		await page.locator('[name="password"]').fill("Temporal123");

		console.log('Clicking login button...');

		// Use Promise.race to handle potential page closure during navigation
		const navigationPromise = page.waitForURL("/dashboard/accounts", { timeout: 30000 });
		const clickPromise = page.locator('[data-test="logInButton"]').click();

		await clickPromise;
		await navigationPromise;

		console.log('Login successful, waiting for page elements...');

		// Wait for the page to load completely
		await page.waitForSelector('[data-test="page-title"]', { timeout: 15000 });

		// Verify we're on the right page
		await expect(page).toHaveURL("/dashboard/accounts");

		console.log('Saving authentication state...');
		await context.storageState({ path: authFile });
		console.log('Authentication setup completed successfully');

	} catch (error) {
		console.error('Authentication setup failed:', error);
		console.log('Current URL:', page.url());
		console.log('Page closed:', page.isClosed());

		// Try to take a screenshot for debugging
		try {
			if (!page.isClosed()) {
				await page.screenshot({ path: 'auth-setup-error.png', fullPage: true });
				console.log('Error screenshot saved');
			}
		} catch (screenshotError) {
			console.log('Could not take screenshot:', screenshotError);
		}

		throw error;
	}
});
