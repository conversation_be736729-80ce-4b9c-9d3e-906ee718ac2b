import { test, expect } from "@playwright/test";

test("visits the app root url", async ({ page }) => {
  // Clear any problematic session data
  await page.goto("/");
  await page.evaluate(() => {
    sessionStorage.clear();
    localStorage.clear();
  });

  // Navigate to login page
  await page.goto("/dashboard/login");

  // Verify the page loads and has basic elements
  await expect(page.locator("body")).toBeVisible();
  await expect(page.locator('[name="email"]')).toBeVisible();
  await expect(page.locator('[name="password"]')).toBeVisible();
});
