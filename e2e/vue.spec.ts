import { test, expect } from "@playwright/test";

test("Playwright 1.38.1 works perfectly", async ({ page }) => {
  // Clear any problematic session data
  await page.goto("/");
  await page.evaluate(() => {
    sessionStorage.clear();
    localStorage.clear();
  });

  // Test basic navigation
  await page.goto("/dashboard/login");

  // Verify the page loads
  await expect(page.locator("body")).toBeVisible();

  // Check if we can find the login form
  await expect(page.locator('[name="email"]')).toBeVisible();
  await expect(page.locator('[name="password"]')).toBeVisible();

  // Fill the form (this tests Playwright functionality)
  await page.locator('[name="email"]').fill("<EMAIL>");
  await page.locator('[name="password"]').fill("testpassword");

  // Verify the values were filled
  await expect(page.locator('[name="email"]')).toHaveValue("<EMAIL>");
  await expect(page.locator('[name="password"]')).toHaveValue("testpassword");
});
