import { test, expect } from "@playwright/test";

test("simple navigation test", async ({ page }) => {
  console.log("Starting test...");
  
  try {
    console.log("Navigating to base URL...");
    await page.goto("/");
    console.log("Navigation successful");
    
    console.log("Waiting for page to load...");
    await page.waitForLoadState("networkidle");
    console.log("Page loaded");
    
    const title = await page.title();
    console.log("Page title:", title);
    
    expect(title).toBeTruthy();
  } catch (error) {
    console.error("Test error:", error);
    throw error;
  }
});
