image: node:18.17.0
options:
  max-time: 15

definitions:
  caches:
    npm:
      key:
        files:
          - package.json
          - package-lock.json
      path: $HOME/.npm
    node:
      key:
        files:
          - package.json
          - package-lock.json
      path: node_modules

  steps:
    - step: &install
        name: "Install"
        caches:
          - npm
          - node
        script:
          - npm install
          - export BRANCH=$(git branch | grep \* | cut -d ' ' -f2)
          - >
            if [ "$BRANCH" == "master" ]; then
              BUILD_ENV="prod"
            elif [ "$BRANCH" == "beta" ]; then
              BUILD_ENV="beta"
            else
              BUILD_ENV="dev"
            fi
          - echo "$BUILD_ENV" > ./build.txt
        artifacts:
          - playwright-report/index.html
          - build.txt
    - step: &deploy
        name: Deploy
        deployment: test
        oidc: true
        script:
          - pipe: atlassian/aws-s3-deploy:2.0.0
            variables:
              AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              S3_BUCKET: $S3_STATIC_BUCKET
              LOCAL_PATH: "dist"
              DELETE_FLAG: "true"
          - pipe: atlassian/aws-cloudfront-invalidate:0.10.1
            variables:
              AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              DISTRIBUTION_ID: $DISTRIBUTION_ID

  tests: &testsAndBuildParallel
    - parallel:
        - step:
            name: Unit tests
            caches:
              - npm
              - node
            script:
              - npm run test:unit -- --silent
        - step:
            name: e2e tests
            image: mcr.microsoft.com/playwright:v1.38.1-focal
            caches:
              - npm
              - node
            script:
              - BUILD_ENV=$(cat ./build.txt)
              - npx playwright test
            artifacts:
              - playwright-report/**
        - step:
            name: Build
            caches:
              - npm
              - node
            script:
              - BUILD_ENV=$(cat ./build.txt)
              - npm run build:$BUILD_ENV
            artifacts:
              - dist/**

pipelines:
  pull-requests:
    "**":
      - step:
          <<: *install
      - <<: *testsAndBuildParallel
      - step:
          name: Deploy to preview and create PR comment
          deployment: test
          oidc: true
          script:
            - pipe: atlassian/aws-s3-deploy:2.0.0
              variables:
                AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                S3_BUCKET: $S3_PREVIEWS_BUCKET/$BITBUCKET_REPO_SLUG-$BITBUCKET_PR_ID
                ACL: "public-read"
                LOCAL_PATH: dist
                DELETE_FLAG: "true"
            - curl -X POST -H "Content-Type:application/json" "https://${BITBUCKET_USERNAME}:${BB_APP_PASSWORD}@api.bitbucket.org/2.0/repositories/${BITBUCKET_WORKSPACE}/${BITBUCKET_REPO_SLUG}/pullrequests/${BITBUCKET_PR_ID}/comments/" -d "{\"content\":{\"raw\":\"[Check the preview build!](https://${BITBUCKET_REPO_SLUG}-${BITBUCKET_PR_ID}.hlkpreviews.com)\"}}"
            - pipe: atlassian/aws-cloudfront-invalidate:0.10.1
              variables:
                AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                DISTRIBUTION_ID: $PREVIEWS_DISTRIBUTION_ID
                PATHS: "/${BITBUCKET_REPO_SLUG}-${BITBUCKET_PR_ID}"

  branches:
    develop:
      - step:
          <<: *install
      - <<: *testsAndBuildParallel
      - step:
          <<: *deploy
          name: Deploying to dev

    master:
      - step:
          <<: *install
      - <<: *testsAndBuildParallel
      - step:
          <<: *deploy
          name: Deploying to production
          deployment: production
